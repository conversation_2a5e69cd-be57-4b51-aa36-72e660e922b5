import request from '@/config/axios'

// 场地信息接口
export interface Site {
  id: number
  name: string
  campus: string
  type: string
  seat: number
  seat_detail?: string
  location: string
  equipment: string
  status: string
  manager: string
  manager_phone: string
  creator?: string
  create_time?: string
  updater?: string
  update_time?: string
}

// 场地预约信息接口
export interface SiteAppointment {
  id?: number
  site_id: number
  activity_name: string
  date: string
  time: string
  type: string
  people_count: number
  contact_name: string
  contact_phone: string
  remark?: string
  status?: string
  creator?: string
  create_time?: string
}

// 场地分页查询参数
export interface SitePageParams {
  pageNo: number
  pageSize: number
  campus?: string
  type?: string
  status?: string
  keyword?: string
}

// 场地分页查询结果
export interface SitePageResult {
  total: number
  list: Site[]
}

// 场地排期看板数据
export interface SiteScheduleBoard {
  id: number
  name: string
  campus: string
  type: string
  capacity: number
  reservations: number[] // 已预约的天数数组
}

// 场地排期看板查询参数
export interface SiteScheduleBoardParams {
  month: string // 格式：YYYY-MM
  campus?: string
  keyword?: string
}

// 新增场地预约参数
export interface CreateSiteAppointmentParams {
  site_id: number
  activity_name: string
  date: string
  time: string
  type: string
  people_count: number
  contact_name: string
  contact_phone: string
  remark?: string
}

// 场地管理API
export const SiteManagementApi = {
  // ==================== 场地管理接口 ====================

  // 场地分页查询
  getSitePage: async (params: SitePageParams): Promise<SitePageResult> => {
    return await request.get({ url: '/publicbiz/site/page', params })
  },

  // 场地详情查询
  getSiteDetail: async (id: number): Promise<Site> => {
    return await request.get({ url: `/publicbiz/site/detail?id=${id}` })
  },

  // 新增场地
  createSite: async (data: Omit<Site, 'id' | 'creator' | 'create_time' | 'updater' | 'update_time'>): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/site/create', data })
  },

  // 编辑场地
  updateSite: async (data: Site): Promise<void> => {
    return await request.post({ url: '/publicbiz/site/update', data })
  },

  // 删除场地
  deleteSite: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/site/delete', data: { id } })
  },

  // ==================== 场地预约接口 ====================

  // 新增场地预约
  createSiteAppointment: async (data: CreateSiteAppointmentParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/site/appointment/create', data })
  },

  // 删除场地预约
  deleteSiteAppointment: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/site/appointment/delete', data: { id } })
  },

  // 场地预约列表查询
  getSiteAppointmentList: async (params: { site_id?: number; date?: string }): Promise<SiteAppointment[]> => {
    return await request.get({ url: '/publicbiz/site/appointment/list', params })
  },

  // ==================== 场地排期看板接口 ====================

  // 场地排期看板数据查询
  getSiteScheduleBoard: async (params: SiteScheduleBoardParams): Promise<SiteScheduleBoard[]> => {
    return await request.get({ url: '/publicbiz/site/schedule/board', params })
  }
}

export default SiteManagementApi
