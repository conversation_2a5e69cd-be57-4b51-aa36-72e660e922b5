<template>
  <div class="scheduling-board">
    <!-- 顶部操作区 -->
    <div class="sb-toolbar">
      <div class="sb-toolbar-left">
        <el-button-group>
          <el-button @click="onPrevMonth"
            ><el-icon><i class="el-icon-arrow-left"></i></el-icon> 上月</el-button
          >
          <el-button @click="onToday">回到今日</el-button>
          <el-button @click="onNextMonth"
            >下月 <el-icon><i class="el-icon-arrow-right"></i></el-icon
          ></el-button>
        </el-button-group>
        <el-date-picker
          v-model="currentDate"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :clearable="false"
          style="width: 120px"
        />
      </div>
      <div class="sb-toolbar-center">
        <el-button :type="viewType === 'month' ? 'primary' : 'default'" @click="viewType = 'month'"
          >月视图</el-button
        >
      </div>
      <div class="sb-toolbar-right">
        <el-select v-model="campus" placeholder="全部校区" style="width: 130px; margin-right: 8px">
          <el-option v-for="item in campusOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <el-input v-model="search" placeholder="搜索场地..." style="width: 160px" clearable />
      </div>
    </div>
    <!-- 主内容区 -->
    <div class="sb-main-card">
      <!-- 固定头部区域 -->
      <div class="sb-table-header-row">
        <div class="sb-table-header-left">
          <div class="sb-table-header">场地列表</div>
        </div>
        <div class="sb-table-header-right">
          <!-- 右侧头部容器，支持横向滚动 -->
          <div class="sb-calendar-header-container" ref="calendarHeaderContainer">
            <div class="sb-calendar-header">
              <div v-for="d in days" :key="d" class="sb-calendar-day">{{ d }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统一滚动容器 -->
      <div class="sb-table-scroll-container">
        <div class="sb-table">
          <div class="sb-table-left">
            <div
              v-for="site in filteredSites"
              :key="site.id"
              class="sb-site-row"
              :class="{ 'sb-site-row--disabled': !isSiteAvailable(site) }"
            >
              <!-- 状态标签 - 右上角 -->
              <el-tag
                :type="getSiteStatusType(site.status)"
                size="small"
                class="sb-site-status sb-site-status--top-right"
              >
                {{ site.status }}
              </el-tag>

              <!-- 场地信息 -->
              <div class="sb-site-content">
                <div class="sb-site-title">
                  {{ site.name }}
                </div>
                <div class="sb-site-desc">
                  {{ site.campus }} · {{ site.type }} · {{ site.capacity }}座
                </div>
              </div>

              <!-- 预约按钮 - 右下角 -->
              <el-button
                size="small"
                type="primary"
                class="sb-site-btn sb-site-btn--bottom-right"
                :disabled="!isSiteAvailable(site)"
                @click="onBookSite(site)"
              >
                +预约
              </el-button>
            </div>
          </div>
          <div class="sb-table-right">
            <!-- 右侧内容容器，支持横向滚动 -->
            <div class="sb-calendar-content-container" ref="calendarContentContainer">
              <div
                v-for="site in filteredSites"
                :key="site.id"
                class="sb-calendar-row"
                :class="{ 'sb-calendar-row--disabled': !isSiteAvailable(site) }"
              >
                <div
                  v-for="d in days"
                  :key="d"
                  class="sb-calendar-cell"
                  :class="{ 'sb-calendar-cell--disabled': !isSiteAvailable(site) }"
                  @click="isSiteAvailable(site) ? onCellClick(site, d) : null"
                >
                  <div
                    v-if="isReserved(site.id, d)"
                    class="sb-reserved-block"
                    :class="{ 'sb-reserved-block--disabled': !isSiteAvailable(site) }"
                    :title="isSiteAvailable(site) ? getReservationTitle(site.id, d) : '场地不可用'"
                    @click.stop="isSiteAvailable(site) ? onReservedBlockClick(site, d) : null"
                  >
                    <!-- 预约块填满单元格，无文本显示 -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约弹窗 -->
    <SiteAppointment
      v-if="appointmentVisible"
      :visible="appointmentVisible"
      :siteInfo="selectedSite"
      :reservedList="reservedList"
      @close="closeAppointment"
      @submit="handleAppointmentSubmit"
    />

    <!-- 预约详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      :title="`${selectedSite?.name} - ${selectedDate} 预约详情`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="dayReservations.length === 0" class="no-reservations">
        <el-empty description="该日期暂无预约安排" />
        <div style="text-align: center; margin-top: 16px">
          <el-button type="primary" @click="onBookSiteFromDetail">立即预约</el-button>
        </div>
      </div>
      <div v-else>
        <div v-for="reservation in dayReservations" :key="reservation.id" class="reservation-item">
          <el-card shadow="never" style="margin-bottom: 12px">
            <div class="reservation-header">
              <h4>{{ reservation.activity_name }}</h4>
              <el-tag :type="reservation.status === '已确认' ? 'success' : 'warning'">
                {{ reservation.status || '已确认' }}
              </el-tag>
            </div>
            <div class="reservation-details">
              <p><strong>时间：</strong>{{ reservation.time }}</p>
              <p><strong>类型：</strong>{{ reservation.type }}</p>
              <p><strong>人数：</strong>{{ reservation.people_count }}人</p>
              <p
                ><strong>联系人：</strong>{{ reservation.contact_name }} ({{
                  reservation.contact_phone
                }})</p
              >
              <p v-if="reservation.remark"><strong>备注：</strong>{{ reservation.remark }}</p>
            </div>
          </el-card>
        </div>
        <div style="text-align: center; margin-top: 16px">
          <el-button type="primary" @click="onBookSiteFromDetail">继续预约</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import SiteAppointment from './SiteAppointment.vue'
import { ElMessage } from 'element-plus'

// 获取当前年月（YYYY-MM）
function getCurrentYearMonth() {
  const now = new Date()
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
}

const currentDate = ref(getCurrentYearMonth())
const viewType = ref('month')
const campus = ref('全部校区')
const search = ref('')

const campusOptions = ['全部校区', '总部校区', '华东分校', '华南分校', '华北分校', '西部分校']

// 横向滚动同步相关的ref
const calendarHeaderContainer = ref<HTMLElement>()
const calendarContentContainer = ref<HTMLElement>()

// 横向滚动同步控制变量
const isHeaderScrolling = ref(false)
const isContentScrolling = ref(false)

const sites = ref([
  {
    id: 1,
    name: '总部A座101教室',
    campus: '总部校区',
    type: '培训教室',
    capacity: 50,
    status: '正常'
  },
  {
    id: 2,
    name: '总部A座201多功能厅',
    campus: '总部校区',
    type: '多功能厅',
    capacity: 120,
    status: '正常'
  },
  {
    id: 3,
    name: '总部B座302会议室',
    campus: '总部校区',
    type: '会议室',
    capacity: 20,
    status: '停用'
  },
  {
    id: 4,
    name: '总部C座实训室',
    campus: '总部校区',
    type: '实训室',
    capacity: 30,
    status: '正常'
  },
  {
    id: 5,
    name: '总部A座203考试专用教室',
    campus: '总部校区',
    type: '考试场地',
    capacity: 40,
    status: '维护中'
  },
  {
    id: 6,
    name: '华东分校小型研讨室',
    campus: '华东分校',
    type: '研讨室',
    capacity: 8,
    status: '正常'
  },
  {
    id: 7,
    name: '华东分校计算机教室',
    campus: '华东分校',
    type: '培训教室',
    capacity: 35,
    status: '正常'
  },
  {
    id: 8,
    name: '华南分校大型会议厅',
    campus: '华南分校',
    type: '多功能厅',
    capacity: 200,
    status: '正常'
  }
])

const days = ref<number[]>([])
function updateDays() {
  const [year, month] = currentDate.value.split('-').map(Number)
  const lastDay = new Date(year, month, 0).getDate()
  days.value = Array.from({ length: lastDay }, (_, i) => i + 1)
}
updateDays()
watch(currentDate, updateDays)

// 预约数据 - 扩展为包含活动名称的完整数据
const reservations = ref([
  {
    id: 1,
    siteId: 4,
    day: 1,
    activity_name: '家政服务员技能培训',
    date: '2025-08-01',
    time: '09:00-17:00',
    type: '培训',
    people_count: 35,
    contact_name: '张老师',
    contact_phone: '13812345678',
    status: '已确认',
    remark: '需要投影设备和音响'
  },
  {
    id: 2,
    siteId: 4,
    day: 15,
    activity_name: '月嫂培训考试',
    date: '2025-08-15',
    time: '14:00-16:00',
    type: '考试',
    people_count: 20,
    contact_name: '李老师',
    contact_phone: '13987654321',
    status: '已确认',
    remark: '考试专用，需要安静环境'
  },
  {
    id: 3,
    siteId: 1,
    day: 10,
    activity_name: '新员工入职培训',
    date: '2025-08-10',
    time: '09:00-12:00',
    type: '培训',
    people_count: 45,
    contact_name: '王老师',
    contact_phone: '13765432109',
    status: '已确认',
    remark: ''
  },
  {
    id: 4,
    siteId: 2,
    day: 5,
    activity_name: '育婴师技能考核',
    date: '2025-08-05',
    time: '10:00-12:00',
    type: '考试',
    people_count: 15,
    contact_name: '陈老师',
    contact_phone: '13654321098',
    status: '已确认',
    remark: '实操考试，需要婴儿模型'
  },
  {
    id: 5,
    siteId: 3,
    day: 8,
    activity_name: '养老护理员培训',
    date: '2025-08-08',
    time: '14:00-18:00',
    type: '培训',
    people_count: 25,
    contact_name: '刘老师',
    contact_phone: '13543210987',
    status: '已确认',
    remark: '需要护理床和相关设备'
  },
  {
    id: 6,
    siteId: 1,
    day: 20,
    activity_name: '保洁技能培训',
    date: '2025-08-20',
    time: '09:00-16:00',
    type: '培训',
    people_count: 30,
    contact_name: '赵老师',
    contact_phone: '13432109876',
    status: '已确认',
    remark: '需要清洁工具和用品'
  },
  {
    id: 7,
    siteId: 5,
    day: 12,
    activity_name: '烹饪技能比赛',
    date: '2025-08-12',
    time: '08:00-17:00',
    type: '比赛',
    people_count: 50,
    contact_name: '孙老师',
    contact_phone: '13321098765',
    status: '已确认',
    remark: '需要厨房设备和食材'
  },
  {
    id: 8,
    siteId: 2,
    day: 25,
    activity_name: '母婴护理讲座',
    date: '2025-08-25',
    time: '15:00-17:00',
    type: '讲座',
    people_count: 40,
    contact_name: '周老师',
    contact_phone: '13210987654',
    status: '已确认',
    remark: '需要音响设备'
  },
  // 同一场地同一天多时段预约测试数据 - 场地4，2025-08-15
  {
    id: 9,
    siteId: 4,
    day: 15,
    activity_name: '家政服务技能培训',
    date: '2025-08-15',
    time: '09:00-12:00',
    type: '培训',
    people_count: 25,
    contact_name: '张主任',
    contact_phone: '13800138001',
    status: '已确认',
    remark: '上午场次，需要投影设备'
  },
  {
    id: 10,
    siteId: 4,
    day: 15,
    activity_name: '育婴师实操考试',
    date: '2025-08-15',
    time: '14:00-16:00',
    type: '考试',
    people_count: 15,
    contact_name: '李考官',
    contact_phone: '13800138002',
    status: '已确认',
    remark: '下午场次，需要婴儿模型和考试用品'
  },
  {
    id: 11,
    siteId: 4,
    day: 15,
    activity_name: '月嫂技能交流会',
    date: '2025-08-15',
    time: '18:00-20:00',
    type: '交流会',
    people_count: 30,
    contact_name: '王老师',
    contact_phone: '13800138003',
    status: '已确认',
    remark: '晚上场次，经验分享和答疑'
  },
  // 同一场地同一天多时段预约测试数据 - 场地1，2025-08-22
  {
    id: 12,
    siteId: 1,
    day: 22,
    activity_name: '新员工岗前培训',
    date: '2025-08-22',
    time: '08:30-11:30',
    type: '培训',
    people_count: 35,
    contact_name: '陈经理',
    contact_phone: '13800138004',
    status: '已确认',
    remark: '上午场次，企业文化和规章制度'
  },
  {
    id: 13,
    siteId: 1,
    day: 22,
    activity_name: '安全生产知识考试',
    date: '2025-08-22',
    time: '13:30-15:30',
    type: '考试',
    people_count: 35,
    contact_name: '刘安全员',
    contact_phone: '13800138005',
    status: '已确认',
    remark: '下午场次，闭卷考试'
  },
  {
    id: 14,
    siteId: 1,
    day: 22,
    activity_name: '团队建设活动',
    date: '2025-08-22',
    time: '16:00-18:00',
    type: '活动',
    people_count: 35,
    contact_name: '赵主管',
    contact_phone: '13800138006',
    status: '已确认',
    remark: '下午后场次，团队协作游戏'
  }
])

// 预约相关状态
const appointmentVisible = ref(false)
const selectedSite = ref<any>({})
const reservedList = ref<any[]>([])

// 预约详情弹窗相关状态
const detailVisible = ref(false)
const selectedDate = ref('')
const dayReservations = ref<any[]>([])

const filteredSites = computed(() => {
  return sites.value.filter(
    (site) =>
      (campus.value === '全部校区' || site.campus === campus.value) &&
      (!search.value || site.name.includes(search.value))
  )
})

const isReserved = (siteId: number, day: number) => {
  return reservations.value.some((r) => r.siteId === siteId && r.day === day)
}

// 判断场地是否可用（正常状态）
const isSiteAvailable = (site: any) => {
  return site.status === '正常'
}

// 获取场地状态标签类型
const getSiteStatusType = (status: string) => {
  switch (status) {
    case '正常':
      return 'success'
    case '停用':
      return 'danger'
    case '维护中':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取预约详情标题（鼠标悬停显示）- 显示该日期所有预约的汇总信息
const getReservationTitle = (siteId: number, day: number) => {
  const dayReservations = reservations.value.filter((r) => r.siteId === siteId && r.day === day)

  if (dayReservations.length === 0) {
    return ''
  }

  // 按时间排序
  const sortedReservations = dayReservations.sort((a, b) => {
    const timeA = a.time.split('-')[0] // 获取开始时间
    const timeB = b.time.split('-')[0]
    return timeA.localeCompare(timeB)
  })

  // 格式化为"活动名称-(时间段)"，多条记录用换行符分隔
  return sortedReservations.map((r) => `${r.activity_name}-(${r.time})`).join('\n')
}

// 点击预约按钮
const onBookSite = (site: any) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法预约`)
    return
  }

  selectedSite.value = { ...site }
  // 获取该场地的已有预约列表
  reservedList.value = reservations.value.filter((r) => r.siteId === site.id)
  appointmentVisible.value = true
}

// 点击日历格子
const onCellClick = (site: any, day: number) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法查看或预约`)
    return
  }

  const [year, month] = currentDate.value.split('-').map(Number)
  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  // 获取该场地当天的预约
  const dayReservationsList = reservations.value.filter(
    (r) => r.siteId === site.id && r.date === dateStr
  )

  // 根据是否有预约数据决定显示弹窗还是提示消息
  if (dayReservationsList.length > 0) {
    // 有预约：显示预约详情弹窗
    selectedSite.value = { ...site }
    selectedDate.value = dateStr
    dayReservations.value = dayReservationsList
    detailVisible.value = true
  } else {
    // 没有预约：显示提示消息
    ElMessage.info(`${site.name} 在 ${dateStr} 没有预约安排`)
  }
}

// 点击预约块
const onReservedBlockClick = (site: any, day: number) => {
  // 检查场地状态
  if (!isSiteAvailable(site)) {
    ElMessage.warning(`场地状态为"${site.status}"，无法查看预约详情`)
    return
  }

  const [year, month] = currentDate.value.split('-').map(Number)
  const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  // 调试信息
  console.log('预约块点击调试信息:', {
    siteId: site.id,
    siteName: site.name,
    day: day,
    dateStr: dateStr,
    currentDate: currentDate.value,
    allReservations: reservations.value
  })

  // 获取该场地当天的预约
  const dayReservationsList = reservations.value.filter(
    (r) => r.siteId === site.id && r.date === dateStr
  )

  console.log('筛选结果:', {
    dayReservationsList: dayReservationsList,
    filterCondition: `siteId=${site.id} && date=${dateStr}`
  })

  // 预约块只在有预约数据时才显示，所以直接显示预约详情弹窗
  if (dayReservationsList.length > 0) {
    selectedSite.value = { ...site }
    selectedDate.value = dateStr
    dayReservations.value = dayReservationsList
    detailVisible.value = true
    console.log('预约详情弹窗已显示')
  } else {
    // 理论上不应该到达这里，因为预约块只在有预约时才显示
    console.error('预约块点击但未找到预约数据，这可能是数据不一致的问题')
    ElMessage.warning(`未找到预约数据。调试信息：场地ID=${site.id}, 日期=${dateStr}`)
  }
}

// 从详情弹窗中预约
const onBookSiteFromDetail = () => {
  detailVisible.value = false
  onBookSite(selectedSite.value)
}

// 关闭预约弹窗
const closeAppointment = () => {
  appointmentVisible.value = false
  selectedSite.value = {}
  reservedList.value = []
}

// 处理预约提交
const handleAppointmentSubmit = async (formData: any) => {
  try {
    // 这里应该调用API保存预约数据
    console.log('预约数据:', formData)

    // TODO: 调用真实API
    // const result = await SiteManagementApi.createSiteAppointment({
    //   site_id: selectedSite.value.id,
    //   activity_name: formData.activityName,
    //   date: formData.startDate,
    //   time: `${formData.startTime}-${formData.endTime}`,
    //   type: formData.activityType,
    //   people_count: formData.peopleCount,
    //   contact_name: formData.contactName,
    //   contact_phone: formData.contactPhone,
    //   remark: formData.remark || ''
    // })

    // 模拟添加预约到本地数据
    const startDate = new Date(formData.startDate)
    const day = startDate.getDate()

    const newReservation = {
      id: Date.now(),
      siteId: selectedSite.value.id,
      day: day,
      activity_name: formData.activityName,
      date: formData.startDate,
      time: `${formData.startTime}-${formData.endTime}`,
      type: formData.activityType,
      people_count: formData.peopleCount,
      contact_name: formData.contactName,
      contact_phone: formData.contactPhone,
      status: formData.status || '已确认',
      remark: formData.remark || ''
    }

    reservations.value.push(newReservation)

    ElMessage.success('预约成功！')
    closeAppointment()
  } catch (error) {
    console.error('预约失败:', error)
    ElMessage.error('预约失败，请重试')
  }
}

const onPrevMonth = () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month - 1
  if (newMonth === 0) {
    newYear -= 1
    newMonth = 12
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
}
const onNextMonth = () => {
  const [year, month] = currentDate.value.split('-').map(Number)
  let newYear = year
  let newMonth = month + 1
  if (newMonth === 13) {
    newYear += 1
    newMonth = 1
  }
  currentDate.value = `${newYear}-${String(newMonth).padStart(2, '0')}`
}

// 横向滚动同步方法
const syncHeaderScroll = (scrollLeft: number) => {
  if (calendarHeaderContainer.value && !isHeaderScrolling.value) {
    isContentScrolling.value = true

    // 计算头部容器的最大滚动距离
    const headerMaxScroll =
      calendarHeaderContainer.value.scrollWidth - calendarHeaderContainer.value.clientWidth
    // 限制滚动距离不超过最大值
    const clampedScrollLeft = Math.min(scrollLeft, headerMaxScroll)

    calendarHeaderContainer.value.scrollLeft = clampedScrollLeft
    nextTick(() => {
      isContentScrolling.value = false
    })
  }
}

const syncContentScroll = (scrollLeft: number) => {
  if (calendarContentContainer.value && !isContentScrolling.value) {
    isHeaderScrolling.value = true

    // 计算内容容器的最大滚动距离
    const contentMaxScroll =
      calendarContentContainer.value.scrollWidth - calendarContentContainer.value.clientWidth
    // 限制滚动距离不超过最大值
    const clampedScrollLeft = Math.min(scrollLeft, contentMaxScroll)

    calendarContentContainer.value.scrollLeft = clampedScrollLeft
    nextTick(() => {
      isHeaderScrolling.value = false
    })
  }
}

// 头部横向滚动事件处理
const handleHeaderScroll = (event: Event) => {
  if (isContentScrolling.value) return

  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft
  syncContentScroll(scrollLeft)
}

// 内容横向滚动事件处理
const handleContentScroll = (event: Event) => {
  if (isHeaderScrolling.value) return

  const target = event.target as HTMLElement
  const scrollLeft = target.scrollLeft
  syncHeaderScroll(scrollLeft)
}

// 确保两个容器的内容宽度一致
const ensureWidthConsistency = () => {
  if (calendarHeaderContainer.value && calendarContentContainer.value) {
    const headerContent = calendarHeaderContainer.value.querySelector('.sb-calendar-header')
    const contentRows = calendarContentContainer.value.querySelectorAll('.sb-calendar-row')

    if (headerContent && contentRows.length > 0) {
      // 计算头部内容的实际宽度
      const headerWidth = headerContent.scrollWidth

      // 确保所有内容行都有相同的宽度
      contentRows.forEach((row) => {
        const rowElement = row as HTMLElement
        rowElement.style.minWidth = `${headerWidth}px`
      })

      // 同时确保头部也有正确的最小宽度
      const headerElement = headerContent as HTMLElement
      headerElement.style.minWidth = `${headerWidth}px`
    }
  }
}

// 初始化横向滚动监听器
const initHorizontalScrollSync = () => {
  if (calendarHeaderContainer.value) {
    calendarHeaderContainer.value.addEventListener('scroll', handleHeaderScroll, { passive: true })
  }
  if (calendarContentContainer.value) {
    calendarContentContainer.value.addEventListener('scroll', handleContentScroll, {
      passive: true
    })
  }

  // 确保宽度一致性
  ensureWidthConsistency()
}

// 清理横向滚动监听器
const cleanupHorizontalScrollSync = () => {
  if (calendarHeaderContainer.value) {
    calendarHeaderContainer.value.removeEventListener('scroll', handleHeaderScroll)
  }
  if (calendarContentContainer.value) {
    calendarContentContainer.value.removeEventListener('scroll', handleContentScroll)
  }
}

// 监听数据变化，重新确保宽度一致性
watch(
  [currentDate, filteredSites],
  () => {
    nextTick(() => {
      ensureWidthConsistency()
    })
  },
  { deep: true }
)

// 组件挂载时初始化横向滚动同步
onMounted(() => {
  nextTick(() => {
    initHorizontalScrollSync()
  })
})

// 组件卸载时清理监听器
onUnmounted(() => {
  cleanupHorizontalScrollSync()
})

const onToday = () => {
  currentDate.value = getCurrentYearMonth()
}
</script>

<style scoped lang="scss">
.scheduling-board {
  padding: 18px;
  background: #f7f8fa;
}
.sb-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.sb-toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-toolbar-center {
  display: flex;
  gap: 8px;
}
.sb-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sb-main-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 18px 0 0 0;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  height: 80vh;
}

// 固定头部区域
.sb-table-header-row {
  display: flex;
  flex-shrink: 0;
  border-bottom: 1px solid #e5e6eb;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sb-table-header-left {
  width: 260px;
  border-right: 1px solid #e5e6eb;
  padding: 0 0 0 18px;
  display: flex;
  align-items: center;
}

.sb-table-header-right {
  flex: 1;
  overflow: hidden; // 防止头部区域产生滚动条
}

// 右侧头部容器 - 支持横向滚动
.sb-calendar-header-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding-left: 18px;

  // 隐藏滚动条但保持滚动功能
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE/Edge

  &::-webkit-scrollbar {
    display: none; // Chrome/Safari
  }
}

// 统一滚动容器
.sb-table-scroll-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
.sb-table {
  display: flex;
  align-items: stretch;
  min-height: 0;
}

.sb-table-left {
  width: 260px;
  border-right: 1px solid #e5e6eb;
  background: #fff;
  padding: 0 0 0 18px;
}
.sb-table-header {
  font-weight: bold;
  font-size: 16px;
  padding: 12px 0;
  background: #fff;
}
.sb-site-row {
  height: 85px; // 增加行高到85px，提供更舒适的间距
  padding: 12px 0; // 增加内边距
  border-bottom: 1px dashed #f0f0f0;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start; // 确保内容左对齐

  &.sb-site-row--disabled {
    background-color: #f5f5f5;
    opacity: 0.6;

    .sb-site-title {
      color: #999;
    }

    .sb-site-desc {
      color: #bbb;
    }
  }
}

// 场地内容区域
.sb-site-content {
  width: calc(100% - 120px); // 为右侧状态标签和按钮留出空间
  padding-right: 8px;
}

.sb-site-title {
  font-weight: 500;
  font-size: 16px; // 稍微增大字体
  line-height: 1.3;
  margin-bottom: 6px; // 增加与描述的间距
  color: #303133;
}

.sb-site-desc {
  color: #888;
  font-size: 13px;
  margin: 0;
  line-height: 1.4; // 增加行高提升可读性
}

// 状态标签 - 右上角定位
.sb-site-status {
  &.sb-site-status--top-right {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 2;
  }
}

// 预约按钮 - 右下角定位
.sb-site-btn {
  &.sb-site-btn--bottom-right {
    position: absolute;
    right: 8px;
    bottom: 8px;
    font-size: 13px;
    padding: 2px 12px;
    border-radius: 6px;
    z-index: 2;
  }
}
.sb-table-right {
  flex: 1;
  overflow: hidden; // 防止表格区域产生滚动条
}

// 右侧内容容器 - 支持横向滚动
.sb-calendar-content-container {
  overflow-x: auto;
  overflow-y: hidden;
  padding-left: 18px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
.sb-calendar-header {
  display: flex;
  padding: 12px 0;
  background: #fff;
  width: fit-content; // 使用fit-content确保宽度精确匹配内容
  min-width: 100%; // 确保至少占满容器宽度
}
.sb-calendar-day {
  flex: 1 1 0;
  min-width: 40px; // 与日期格子保持一致的最小宽度
  text-align: center;
  color: #888;
  font-size: 13px;
  padding: 2px 0;
  box-sizing: border-box; // 确保盒模型一致
}
.sb-calendar-row {
  display: flex;
  height: 85px; // 与左侧场地行高度保持一致
  flex: none; // 改为固定高度，不使用flex伸缩
  align-items: center; // 确保内容垂直居中对齐
  width: fit-content; // 使用fit-content确保宽度精确匹配内容
  min-width: 100%; // 确保至少占满容器宽度

  &.sb-calendar-row--disabled {
    background-color: #f9f9f9;
    opacity: 0.6;
  }
}
.sb-calendar-cell {
  flex: 1 1 0;
  min-width: 40px; // 与日期头部保持一致的最小宽度
  height: 85px; // 与行高保持一致
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
  box-sizing: border-box; // 确保盒模型一致
  position: relative; // 为预约块的绝对定位提供定位上下文

  &:hover {
    background-color: #f8f9fa;
  }

  &.sb-calendar-cell--disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
.sb-reserved-block {
  width: 100%; // 填满整个日历格子宽度
  height: 100%; // 填满整个日历格子高度
  background: #e57373;
  border-radius: 4px; // 稍微减小圆角，适应填满效果
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0; // 隐藏文本，因为不再显示文字
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  position: absolute; // 绝对定位，确保填满父容器
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  &:hover {
    background: #d32f2f;
    transform: none; // 移除缩放效果，避免影响布局
    opacity: 0.9; // 使用透明度变化作为hover效果
  }

  &.sb-reserved-block--disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;

    &:hover {
      background: #ccc;
      opacity: 1;
      transform: none;
    }
  }
}

// 预约详情弹窗样式
.reservation-item {
  .reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }
  }

  .reservation-details {
    p {
      margin: 8px 0;
      color: #606266;
      font-size: 14px;

      strong {
        color: #303133;
        margin-right: 8px;
      }
    }
  }
}

.no-reservations {
  text-align: center;
  padding: 20px;
}

// 超大屏幕 (>1400px) - 提供最舒适的行高
@media (min-width: 1400px) {
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 95px; // 超大屏幕使用更大的行高
  }

  .sb-site-row {
    padding: 15px 0; // 增加内边距
  }

  .sb-site-title {
    font-size: 17px; // 增大字体
    margin-bottom: 8px;
  }

  .sb-site-desc {
    font-size: 14px; // 增大字体
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 6px; // 超大屏幕使用稍大的圆角
  }

  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 45px; // 增加最小宽度，保持一致
  }
}

@media (max-width: 1200px) {
  .sb-main-card {
    height: auto;
    min-height: 400px;
  }
  .sb-table-left {
    width: 180px;
  }
  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 28px; // 稍微增加最小宽度
  }

  // 保持行高一致 - 中等屏幕使用70px
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 70px;
  }

  .sb-site-row {
    padding: 10px 0; // 调整内边距
  }

  .sb-site-title {
    font-size: 15px;
    margin-bottom: 5px;
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 3px; // 中等屏幕使用中等圆角
  }

  // 中等屏幕的布局调整
  .sb-site-content {
    width: calc(100% - 100px); // 调整内容区域宽度
  }

  .sb-site-status--top-right {
    top: 6px;
    right: 6px;
  }

  .sb-site-btn--bottom-right {
    right: 6px;
    bottom: 6px;
    font-size: 12px;
    padding: 1px 10px;
  }
}

@media (max-width: 900px) {
  .sb-main-card {
    height: auto;
    min-height: 300px;
  }
  .sb-table-left {
    width: 120px;
  }
  .sb-calendar-day,
  .sb-calendar-cell {
    min-width: 22px; // 稍微增加最小宽度
    font-size: 11px;
  }

  // 保持行高一致 - 小屏幕使用60px，仍然比原来的45px更舒适
  .sb-site-row,
  .sb-calendar-row,
  .sb-calendar-cell {
    height: 60px;
  }

  .sb-site-row {
    padding: 8px 0; // 调整内边距
  }

  .sb-site-title {
    font-size: 14px; // 稍微增大字体
    margin-bottom: 4px;
  }

  .sb-site-desc {
    font-size: 12px; // 稍微增大字体
  }

  .sb-site-btn {
    font-size: 11px;
    padding: 2px 8px; // 稍微增加内边距
  }

  .sb-reserved-block {
    // 预约块填满格子，无需设置高度
    border-radius: 2px; // 小屏幕使用小圆角
  }

  // 小屏幕的布局调整
  .sb-site-content {
    width: calc(100% - 80px); // 进一步调整内容区域宽度
  }

  .sb-site-status--top-right {
    top: 4px;
    right: 4px;
    transform: scale(0.9); // 稍微缩小标签
  }

  .sb-site-btn--bottom-right {
    right: 4px;
    bottom: 4px;
    font-size: 10px;
    padding: 1px 6px;
    transform: scale(0.9); // 稍微缩小按钮
  }
}
</style>
